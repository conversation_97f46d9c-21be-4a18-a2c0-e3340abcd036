<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="./animation.css">    
    <title>动画组件</title>
</head>
<style>
    .cl-seamless-container {
    overflow: hidden;
}

.cl-seamless-list {
    margin: auto 0;
    padding: auto 0;
    /* 滚动快慢会受滚动列表项的高度影响，在js中调节速率进行控制 */
    /* animation: cl-scrolling 6s infinite linear; */
}

/* 鼠标移入滚动区域变成手型 */
.cl-seamless-container:hover .cl-seamless-list {
    cursor: pointer;
}

/* 鼠标移入滚动区域暂停动画 */
.cl-seamless-container.pause .cl-seamless-list {
    animation-play-state: paused !important;
}

/* 动画关键帧 */
@keyframes cl-scrolling {
    from {
        transform: translate(0px, 0%);
    }

    to {
        transform: translate(0px, -100%);
    }
}
</style>
<body>
    <h3>无缝滚动组件示例一</h3>
    <div class="cl-seamless-container">
        <ul class="cl-seamless-list">
            <li>滚动项1</li>
            <li>滚动项2</li>
            <li>滚动项3</li>
        </ul>
    </div>

    <h3>无缝滚动组件示例二</h3>
    <div class="cl-seamless-container">
        <ul class="cl-seamless-list">
            <li>滚动项1</li>
            <li>滚动项2</li>
            <li>滚动项3</li>
            <li>滚动项4</li>
            <li>滚动项5</li>
            <li>滚动项6</li>
            <li>滚动项7</li>
            <li>滚动项8</li>
            <li>滚动项9</li>
            <li>滚动项10</li>
        </ul>
    </div>
    <script type="module" src="./animation.js"></script>
</body>

</html>


<script>
function animation () {
    var seamless_container = document.getElementsByClassName("cl-seamless-container");
    for (let i = 0; i < seamless_container.length; i++) {
        if (seamless_container[i].children.length > 0) {
            // 限制每个容器的高度
            let seamless_sub = seamless_container[i].children[0];
            seamless_container[i].style="max-height: " + seamless_sub.clientHeight + "px";
            // 调节滚动速率，使滚动速率不至于过快或过慢
            let scroll_speed = Math.max(4, Math.ceil(seamless_sub.clientHeight / 35));
            seamless_sub.style = "animation: cl-scrolling " + scroll_speed + "s infinite linear";
            // 复制一份完全相同的列表，实现无缝滚动
            seamless_container[i].innerHTML += seamless_container[i].innerHTML;
        }

        // 监听鼠标移入事件，暂停动画; 同时鼠标移出，继续动画
        seamless_container[i].addEventListener("mouseenter", function(event) {
            event.target.classList.add("pause");
            
        });
        seamless_container[i].addEventListener("mouseleave", function(event) {
            event.target.classList.remove("pause");
        })
    }
}

animation();
</script>