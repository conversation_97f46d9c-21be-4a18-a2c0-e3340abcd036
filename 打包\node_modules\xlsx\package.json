{"name": "xlsx", "version": "0.18.5", "author": "sheetjs", "description": "SheetJS Spreadsheet data parser and writer", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "bin": {"xlsx": "./bin/xlsx.njs"}, "main": "xlsx.js", "module": "xlsx.mjs", "unpkg": "dist/xlsx.full.min.js", "jsdelivr": "dist/xlsx.full.min.js", "types": "types/index.d.ts", "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "sideEffects": false, "dependencies": {"adler-32": "~1.3.0", "cfb": "~1.2.1", "codepage": "~1.15.0", "crc-32": "~1.2.1", "ssf": "~0.11.2", "wmf": "~1.0.1", "word": "~0.3.0"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "acorn": "7.4.1", "alex": "8.1.1", "blanket": "~1.2.3", "commander": "~2.17.1", "dtslint": "^0.1.2", "eslint": "7.23.0", "eslint-plugin-html": "^6.1.2", "eslint-plugin-json": "^2.1.2", "exit-on-epipe": "~1.0.1", "fflate": "^0.7.1", "jsdom": "~11.1.0", "markdown-spellcheck": "^1.3.1", "mocha": "~2.5.3", "sinon": "^1.17.7", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/sheetjs.git"}, "scripts": {"pretest": "npm run lint", "test": "npm run tests-only", "pretest-only": "git submodule init && git submodule update", "tests-only": "make travis", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "xlsx.js"}}, "alex": {"allow": ["chinese", "special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "https://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/sheetjs/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}}