const XLSX = require('xlsx');

// Load Excel file
function loadExcelFile(filePath) {
    try {
        const workbook = XLSX.readFile(filePath);
        return workbook;
    } catch (error) {
        console.error('Error loading Excel file:', error);
        throw error;
    }
}

// Get team members from Excel
function getTeamMembers(workbook) {
    const sheet = workbook.Sheets['team_members'];
    if (!sheet) {
        throw new Error('Team members sheet not found');
    }
    return XLSX.utils.sheet_to_json(sheet);
}

// Get work orders from Excel
function getWorkOrders(workbook) {
    const sheet = workbook.Sheets['work_orders'];
    if (!sheet) {
        throw new Error('Work orders sheet not found');
    }
    return XLSX.utils.sheet_to_json(sheet);
}

// Get navigators from Excel
function getNavigators(data) {
    try {
        const sheet = data.Sheets['领航员'];
        if (!sheet) return [];

        const range = XLSX.utils.decode_range(sheet['!ref']);
        const navigators = [];

        // 从第二行开始读取数据（跳过表头）
        for (let R = 1; R <= range.e.r; R++) {
            const name = sheet[XLSX.utils.encode_cell({r: R, c: 0})]?.v;
            const avatar = sheet[XLSX.utils.encode_cell({r: R, c: 1})]?.v;
            const introduction = sheet[XLSX.utils.encode_cell({r: R, c: 2})]?.v;

            if (name && avatar) {
                navigators.push({
                    name,
                    avatar,
                    introduction
                });
            }
        }

        return navigators;
    } catch (error) {
        console.error('Error getting navigators:', error);
        return [];
    }
}

module.exports = {
    loadExcelFile,
    getTeamMembers,
    getWorkOrders,
    getNavigators
}; 