const express = require('express');
const path = require('path');
const { loadExcelFile, getTeamMembers, getWorkOrders, getNavigators } = require('./data-loader');
const fs = require('fs');

const app = express();
const port = process.env.PORT || 3000;

// Serve static files
app.use(express.static('.'));

// Store last modified time
let lastModified = null;

// Load Excel data
let excelData = null;
function loadData() {
    try {
        const stats = fs.statSync('data.xlsx');
        lastModified = stats.mtime.getTime();
        excelData = loadExcelFile('data.xlsx');
        return true;
    } catch (error) {
        console.error('Error loading Excel data:', error);
        return false;
    }
}

// Initial data load
if (!loadData()) {
    process.exit(1);
}

// Watch for file changes
fs.watch('data.xlsx', (eventType, filename) => {
    if (eventType === 'change') {
        const stats = fs.statSync('data.xlsx');
        const currentModified = stats.mtime.getTime();
        
        // Only reload if the file has actually changed
        if (currentModified !== lastModified) {
            console.log('Excel file changed, reloading data...');
            if (loadData()) {
                // Notify all connected clients
                io.emit('dataUpdated');
            }
        }
    }
});

// API endpoints
app.get('/api/team-members', (req, res) => {
    try {
        const members = getTeamMembers(excelData);
        res.json(members);
    } catch (error) {
        console.error('Error fetching team members:', error);
        res.status(500).json({ error: 'Failed to fetch team members' });
    }
});

app.get('/api/work-orders', (req, res) => {
    try {
        const orders = getWorkOrders(excelData);
        res.json(orders);
    } catch (error) {
        console.error('Error fetching work orders:', error);
        res.status(500).json({ error: 'Failed to fetch work orders' });
    }
});

app.get('/api/navigators', (req, res) => {
    try {
        const navigators = getNavigators(excelData);
        res.json(navigators);
    } catch (error) {
        console.error('Error fetching navigators:', error);
        res.status(500).json({ error: 'Failed to fetch navigators' });
    }
});

// Create HTTP server
const server = require('http').createServer(app);

// Initialize Socket.IO
const io = require('socket.io')(server);

// Socket.IO connection handling
io.on('connection', (socket) => {
    console.log('Client connected');
    
    socket.on('disconnect', () => {
        console.log('Client disconnected');
    });
});

// Start server
server.listen(port, () => {
    console.log(`Server running at http://localhost:${port}`);
}); 