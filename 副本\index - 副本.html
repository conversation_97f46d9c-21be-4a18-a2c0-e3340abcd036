<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精英团队展示</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: url('背景.jpg') no-repeat center center;
            background-size: 100% 100%;
            background-attachment: fixed;
            min-height: 100vh;
        }
        .glass-card {
            display: flex;
            flex-direction: row;
            align-items: center;
            padding: 1.2rem;
            min-height: 200px;
            background: rgba(0, 0, 0, 0.2) !important;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            width: 680px;
            margin-left: auto;
            margin-right: auto;
            color: white;
        }
        .glass-card img {
            width: 100px;
            height: 100px;
            margin-right: 0rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        .glass-card-content {
            flex: 1;
        }
        .hover-scale {
            transition: transform 0.3s ease;
        }
        .hover-scale:hover {
            transform: translateY(-5px);
        }
        @keyframes cl-scrolling {
            0% { transform: translateY(0); }
            100% { transform: translateY(-50%); }
        }
        .team-carousel-vertical {
            animation: cl-scrolling linear infinite;
            margin: 0;
            padding: 0;
        }
        .pause .team-carousel-vertical {
            animation-play-state: paused;
        }
        .team-container:hover .team-carousel-vertical {
            animation-play-state: paused;
        }
        .team-container {
            overflow: hidden;
            position: relative;
            min-height: 500px;
            margin-right: 5rem;
        }
        .team-page {
            display: none;
        }
        .team-page.active {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 1.5rem;
            height: 100%;
            padding-left: 0.5rem;
        }
        .pagination-dots {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 1.5rem;
        }
        .pagination-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #ccc;
            cursor: pointer;
        }
        .pagination-dot.active {
            background-color: #3b82f6;
        }
        @media (max-width: 1024px) {
            .team-page.active {
                grid-template-rows: unset;
                grid-template-columns: 1fr;
            }
        }
        @media (max-width: 640px) {
            .team-page.active {
                grid-template-columns: 1fr;
            }
        }
        .glass-card h3,
        .glass-card p,
        .glass-card .text-gray-800 {
            color: white !important;
        }
        .glass-card .text-gray-700 {
            color: rgba(255, 255, 255, 0.9) !important;
        }
        .glass-card .text-blue-600 {
            color: #93c5fd !important;
        }
        .glass-card .bg-blue-100 {
            background: rgba(147, 197, 253, 0.2) !important;
        }
        .glass-card .text-blue-800 {
            color: #bfdbfe !important;
        }
    </style>
</head>
<body class="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto">
        <!-- 标题区 -->
        <div class="mb-12 text-center">
            <img src="NOC战新能力榜.png" alt="团队标题" class="mx-auto h-24 w-auto mb-6">
            <!--h1 class="text-4xl font-bold text-gray-800 mb-4">我们的精英团队</h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">汇聚行业顶尖人才，为您提供专业服务</p-->
        </div>

        <div class="flex flex-col lg:flex-row gap-8 items-stretch">
            <!-- 左侧团队卡片区 -->
            <div class="lg:w-2/3 team-container" style="display: flex; flex-direction: column; min-height: 740px; width: 120%; margin-right: 2rem;">
                <div class="relative overflow-hidden" style="height: 740px;">
                    <!-- Vertical carousel container -->
                    <div class="team-carousel-vertical transition-transform duration-500 ease-in-out" style="height: 200%;">
                        <!-- Team members list -->
                        <div class="team-member-list">
                            <!-- Member 1 -->
                            <div class="glass-card rounded-xl shadow-lg mb-4 hover-scale w-full md:w-11/12 lg:w-10/12" style="min-height: 120px;">
                                <div class="flex items-center h-full">
                                    <img src="刘梅-工作照.png" alt="成员照片" class="rounded-full object-cover border-4 border-white shadow" style="width: 100px; height: 100px; margin-right: 1.5rem;">
                                    <div class="glass-card-content">
                                        <div class="flex items-center mb-2">
                                            <div>
                                                <h3 class="text-xl font-bold text-gray-800">张伟</h3>
                                                <p class="text-blue-600">技术研发团队</p>
                                            </div>
                                        </div>
                                        <p class="text-gray-700 mb-3">10年行业经验，专注于人工智能算法研发，带领团队完成多个国家级重点项目。10年行业经验，专注于人工智能算法研发，带领团队完成多个国家级重点项目。</p>
                                        <div class="flex flex-wrap gap-2">
                                            <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">机器学习</span>
                                            <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">Python</span>
                                            <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">TensorFlow</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Member 2 -->
                            <div class="glass-card rounded-xl shadow-lg mb-4 hover-scale w-full md:w-11/12 lg:w-10/12" style="min-height: 120px;">
                                <div class="flex items-center h-full">
                                    <img src="黄潇聪-证件照.png" alt="成员照片" class="rounded-full object-cover border-4 border-white shadow" style="width: 100px; height: 100px; margin-right: 1.5rem;">
                                    <div class="glass-card-content">
                                        <div class="flex items-center mb-2">
                                            <div>
                                                <h3 class="text-xl font-bold text-gray-800">李娜</h3>
                                                <p class="text-blue-600">产品设计团队</p>
                                            </div>
                                        </div>
                                        <p class="text-gray-700 mb-3">8年用户体验设计经验，主导设计多款行业领先产品。</p>
                                        <div class="flex flex-wrap gap-2">
                                            <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">UI设计</span>
                                            <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">交互设计</span>
                                            <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">用户研究</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                             <!-- Member 2 -->
                            <div class="glass-card rounded-xl shadow-lg mb-4 hover-scale w-full md:w-11/12 lg:w-10/12" style="min-height: 120px;">
                                <div class="flex items-center h-full">
                                    <img src="黄潇聪-证件照.png" alt="成员照片" class="rounded-full object-cover border-4 border-white shadow" style="width: 100px; height: 100px; margin-right: 1.5rem;">
                                    <div class="glass-card-content">
                                        <div class="flex items-center mb-2">
                                            <div>
                                                <h3 class="text-xl font-bold text-gray-800">李娜2</h3>
                                                <p class="text-blue-600">产品设计团队</p>
                                            </div>
                                        </div>
                                        <p class="text-gray-700 mb-3">8年用户体验设计经验，主导设计多款行业领先产品。</p>
                                        <div class="flex flex-wrap gap-2">
                                            <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">UI设计</span>
                                            <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">交互设计</span>
                                            <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">用户研究</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                             <!-- Member 2 -->
                            <div class="glass-card rounded-xl shadow-lg mb-4 hover-scale w-full md:w-11/12 lg:w-10/12" style="min-height: 120px;">
                                <div class="flex items-center h-full">
                                    <img src="黄潇聪-证件照.png" alt="成员照片" class="rounded-full object-cover border-4 border-white shadow" style="width: 100px; height: 100px; margin-right: 1.5rem;">
                                    <div class="glass-card-content">
                                        <div class="flex items-center mb-2">
                                            <div>
                                                <h3 class="text-xl font-bold text-gray-800">李娜3</h3>
                                                <p class="text-blue-600">产品设计团队</p>
                                            </div>
                                        </div>
                                        <p class="text-gray-700 mb-3">8年用户体验设计经验，主导设计多款行业领先产品。</p>
                                        <div class="flex flex-wrap gap-2">
                                            <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">UI设计</span>
                                            <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">交互设计</span>
                                            <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">用户研究</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                             <!-- Member 2 -->
                            <div class="glass-card rounded-xl shadow-lg mb-4 hover-scale w-full md:w-11/12 lg:w-10/12" style="min-height: 120px;">
                                <div class="flex items-center h-full">
                                    <img src="黄潇聪-证件照.png" alt="成员照片" class="rounded-full object-cover border-4 border-white shadow" style="width: 100px; height: 100px; margin-right: 1.5rem;">
                                    <div class="glass-card-content">
                                        <div class="flex items-center mb-2">
                                            <div>
                                                <h3 class="text-xl font-bold text-gray-800">李娜4</h3>
                                                <p class="text-blue-600">产品设计团队</p>
                                            </div>
                                        </div>
                                        <p class="text-gray-700 mb-3">8年用户体验设计经验，主导设计多款行业领先产品。</p>
                                        <div class="flex flex-wrap gap-2">
                                            <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">UI设计</span>
                                            <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">交互设计</span>
                                            <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">用户研究</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                             <!-- Member 2 -->
                            <div class="glass-card rounded-xl shadow-lg mb-4 hover-scale w-full md:w-11/12 lg:w-10/12" style="min-height: 120px;">
                                <div class="flex items-center h-full">
                                    <img src="黄潇聪-证件照.png" alt="成员照片" class="rounded-full object-cover border-4 border-white shadow" style="width: 100px; height: 100px; margin-right: 1.5rem;">
                                    <div class="glass-card-content">
                                        <div class="flex items-center mb-2">
                                            <div>
                                                <h3 class="text-xl font-bold text-gray-800">李娜5</h3>
                                                <p class="text-blue-600">产品设计团队</p>
                                            </div>
                                        </div>
                                        <p class="text-gray-700 mb-3">8年用户体验设计经验，主导设计多款行业领先产品。</p>
                                        <div class="flex flex-wrap gap-2">
                                            <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">UI设计</span>
                                            <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">交互设计</span>
                                            <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">用户研究</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- More members... -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧排行榜区 -->
            <div class="lg:w-1/3 space-y-4 flex flex-col justify-between">
                <div class="glass-card rounded-xl p-6 shadow-lg" style="min-height: 200px; width: 500px; height: 420px;">
                    <div class="text-center w-full">
                        <div class="ranking-title" style="display: flex; align-items: center; justify-content: center; margin-bottom: 0px; font-size: 24px; font-weight: bold; color: white;">
                            <i class="fas fa-trophy text-yellow-400 mr-2"></i>工单排行
                        </div>
                        
                        <div class="top-ranking" style="display: flex; justify-content: center; align-items: flex-end; margin-bottom: 30px;">
                            <!-- 第二名 -->
                            <div class="rank-card no2" style="display: flex; flex-direction: column; align-items: center; margin: 0 25px; margin-bottom: 30px;">
                                <div class="crown" style="position: relative; width: 60px; height: 40px; margin-bottom: 10px;">
                                    <div style="font-size: 40px; color: #42a5f5; position: absolute; left: 50%; transform: translateX(-50%);">👑</div>
                                </div>
                                <div class="rank-banner" style="background-color: #42a5f5; color: white; padding: 5px 20px; border-radius: 15px; margin-bottom: 15px; font-weight: bold; font-size: 18px;">NO.2</div>
                                <img src="黄潇聪-证件照.png" alt="第二名" class="avatar" style="width: 100px; height: 100px; border-radius: 50%; border: 3px solid #42a5f5; margin-bottom: 15px; object-fit: cover;">
                                <div class="player-name" style="font-size: 16px; margin-bottom: 10px; color: #e0e0e0;">李娜</div>
                                <div class="votes" style="font-size: 14px; color: #b0bec5;">3425346票</div>
                            </div>
                            
                            <!-- 第一名 -->
                            <div class="rank-card no1" style="display: flex; flex-direction: column; align-items: center; margin: 0 25px; margin-bottom: 60px;">
                                <div class="crown" style="position: relative; width: 60px; height: 40px; margin-bottom: 10px;">
                                    <div style="font-size: 40px; color: gold; position: absolute; left: 50%; transform: translateX(-50%);">👑</div>
                                </div>
                                <div class="rank-banner" style="background-color: #e53935; color: white; padding: 5px 20px; border-radius: 15px; margin-bottom: 15px; font-weight: bold; font-size: 18px;">NO.1</div>
                                <img src="刘梅-工作照.png" alt="第一名" class="avatar" style="width: 100px; height: 100px; border-radius: 50%; border: 3px solid #e53935; margin-bottom: 15px; object-fit: cover;">
                                <div class="player-name" style="font-size: 16px; margin-bottom: 10px; color: #e0e0e0;">张伟</div>
                                <div class="votes" style="font-size: 14px; color: #b0bec5;">4657546票</div>
                            </div>
                            
                            <!-- 第三名 -->
                            <div class="rank-card no3" style="display: flex; flex-direction: column; align-items: center; margin: 0 25px; margin-bottom: 30px;">
                                <div class="crown" style="position: relative; width: 60px; height: 40px; margin-bottom: 10px;">
                                    <div style="font-size: 40px; color: #ff7043; position: absolute; left: 50%; transform: translateX(-50%);">👑</div>
                                </div>
                                <div class="rank-banner" style="background-color: #ff7043; color: white; padding: 5px 20px; border-radius: 15px; margin-bottom: 15px; font-weight: bold; font-size: 18px;">NO.3</div>
                                <img src="张昕01.png" alt="第三名" class="avatar" style="width: 100px; height: 100px; border-radius: 50%; border: 3px solid #ff7043; margin-bottom: 15px; object-fit: cover;">
                                <div class="player-name" style="font-size: 16px; margin-bottom: 10px; color: #e0e0e0;">王强</div>
                                <div class="votes" style="font-size: 14px; color: #b0bec5;">2453654票</div>
                            </div>
                        </div>

                    
                    </div>
                </div>

                <!-- Update the navigation card section -->
                    <!-- Title -->
                    <div class="glass-card rounded-xl p-6 shadow-lg mt-4" style="min-height: 200px; width: 500px; height: 300px;">
                        <div class="text-center w-full">
                            <div class="ranking-title" style="display: flex; align-items: center; justify-content: center; margin-bottom: 15px; font-size: 24px; font-weight: bold; color: white;">
                                <i class="fas fa-trophy text-yellow-400 mr-2"></i>本周云网导航员
                            </div>
                            
                            <div class="top-ranking" style="display: flex; justify-content: center; align-items: flex-end; margin-bottom: 30px;">
                                <!-- 第二名 -->
                                <div class="rank-card no2" style="display: flex; flex-direction: column; align-items: center; margin: 0 25px; margin-bottom: 15px;">
                                    <img src="黄潇聪-证件照.png" alt="第二名" class="avatar" style="width: 100px; height: 100px; border-radius: 50%; border: 3px solid #42a5f5; margin-bottom: 15px; object-fit: cover;">
                                    <div class="player-name" style="font-size: 16px; margin-bottom: 10px; color: #e0e0e0;">李娜</div>
                                    <div class="votes" style="font-size: 14px; color: #b0bec5;">3425346票</div>
                                </div>
                                
                                <!-- 第一名 -->
                                <div class="rank-card no1" style="display: flex; flex-direction: column; align-items: center; margin: 0 25px; margin-bottom: 15px;">
                                    <img src="刘梅-工作照.png" alt="第一名" class="avatar" style="width: 100px; height: 100px; border-radius: 50%; border: 3px solid #e53935; margin-bottom: 15px; object-fit: cover;">
                                    <div class="player-name" style="font-size: 16px; margin-bottom: 10px; color: #e0e0e0;">张伟</div>
                                    <div class="votes" style="font-size: 14px; color: #b0bec5;">4657546票</div>
                                </div>
                            
                            </div>
                        </div>
                    </div>
            </div>
        </div>

        <!-- 页脚 -->
        <!--footer class="mt-16 text-center text-gray-600">
            <p>© 2023 公司名称. 保留所有权利.</p>
        </footer-->
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const carouselWrapper = document.querySelector('.team-container');
            const carousel = document.querySelector('.team-carousel-vertical');
            const memberList = document.querySelector('.team-member-list');
            
            // 克隆成员列表实现无缝滚动
            carouselWrapper.style.overflow = 'hidden';
            carouselWrapper.style.position = 'relative';
            carousel.innerHTML += carousel.innerHTML;

            // 设置动画参数
            const itemHeight = carousel.clientHeight / 2;
            const scrollSpeed = Math.max(10, Math.ceil(itemHeight / 35));
            
            // 应用动画样式
            carousel.style.animation = `cl-scrolling ${scrollSpeed}s linear infinite`;
            carousel.style.animationName = 'cl-scrolling';

            // 鼠标悬停控制
            carouselWrapper.addEventListener('mouseenter', () => {
                carouselWrapper.classList.add('pause');
            });
            carouselWrapper.addEventListener('mouseleave', () => {
                carouselWrapper.classList.remove('pause');
            });
        });
    </script>
</body>
</html>
