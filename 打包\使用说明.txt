团队展示系统 - 使用说明
========================

快速启动：
---------
Windows用户：双击 start.bat
Linux/Mac用户：运行 ./start.sh

手动启动：
---------
1. 确保已安装Node.js (https://nodejs.org/)
2. 打开命令行，进入此文件夹
3. 运行：npm install
4. 运行：npm start
5. 浏览器访问：http://localhost:3000

文件说明：
---------
index.html      - 主页面
api.js          - 前端逻辑
server.js       - 服务器
data-loader.js  - 数据加载器
data.xlsx       - 数据源（可编辑）
images/         - 头像图片文件夹
package.json    - 项目配置

数据更新：
---------
1. 编辑 data.xlsx 文件
2. 添加新头像到 images/ 文件夹
3. 保存后系统自动更新

注意事项：
---------
- 头像文件名必须与Excel中完全匹配
- 支持jpg、jpeg、png格式
- 建议使用正方形头像图片
- 端口3000被占用时会自动使用其他端口

技术支持：
---------
详细说明请查看 README.md 文件
