-- 确保 noc 模式存在
CREATE SCHEMA IF NOT EXISTS noc;

-- 设置搜索路径
SET search_path TO noc;

-- 创建团队成员表
CREATE TABLE IF NOT EXISTS team_members (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    team VARCHAR(100) NOT NULL,
    description TEXT,
    avatar VARCHAR(255),
    skills TEXT[],
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建工单排行表
CREATE TABLE IF NOT EXISTS work_orders (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    avatar VARCHAR(255),
    orders INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建导航员表
CREATE TABLE IF NOT EXISTS navigators (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    avatar VARCHAR(255),
    orders INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入示例数据
INSERT INTO team_members (name, team, description, avatar, skills) VALUES
    ('张伟', '技术研发团队', '10年行业经验，专注于人工智能算法研发，带领团队完成多个国家级重点项目。', '刘梅-工作照.png', ARRAY['机器学习', 'Python']),
    ('李娜', '产品设计团队', '8年用户体验设计经验，主导设计多款行业领先产品。', '黄潇聪-证件照.png', ARRAY['UI设计', '交互设计']),
    ('李娜2', '产品设计团队', '8年用户体验设计经验，主导设计多款行业领先产品。', '黄潇聪-证件照.png', ARRAY['UI设计', '交互设计']),
    ('李娜3', '产品设计团队', '8年用户体验设计经验，主导设计多款行业领先产品。', '黄潇聪-证件照.png', ARRAY['UI设计', '交互设计']),
    ('李娜4', '产品设计团队', '8年用户体验设计经验，主导设计多款行业领先产品。', '黄潇聪-证件照.png', ARRAY['UI设计', '交互设计']),
    ('李娜5', '产品设计团队', '8年用户体验设计经验，主导设计多款行业领先产品。', '黄潇聪-证件照.png', ARRAY['UI设计', '交互设计']),
    ('李娜6', '产品设计团队', '8年用户体验设计经验，主导设计多款行业领先产品。', '黄潇聪-证件照.png', ARRAY['UI设计', '交互设计']),
    ('李娜7', '产品设计团队', '8年用户体验设计经验，主导设计多款行业领先产品。', '黄潇聪-证件照.png', ARRAY['UI设计', '交互设计']),
    ('李娜8', '产品设计团队', '8年用户体验设计经验，主导设计多款行业领先产品。', '黄潇聪-证件照.png', ARRAY['UI设计', '交互设计']);

INSERT INTO work_orders (name, avatar, orders) VALUES
    ('张伟', '刘梅-工作照.png', 4657546),
    ('李娜1', '黄潇聪-证件照.png', 3425346),
    ('王强', '张昕01.png', 2453654);

INSERT INTO navigators (name, avatar, orders) VALUES
    ('张伟', '刘梅-工作照.png', 4657546),
    ('李娜', '黄潇聪-证件照.png', 3425346); 