# Team Display Application

A web application that displays team members, work orders, and navigators with dynamic data from PostgreSQL database.

## Prerequisites

- Node.js (v14 or higher)
- PostgreSQL (v12 or higher)

## Setup

1. Install dependencies:
```bash
npm install
```

2. Configure database:
   - Create a PostgreSQL database named `team_display`
   - Copy `.env.example` to `.env` and update the database credentials
   - Run the database initialization script:
```bash
psql -U your_username -d team_display -f init-db.sql
```

## Running the Application

1. Start the server:
```bash
npm start
```

2. Open your browser and navigate to:
```
http://localhost:3000
```

## Project Structure

- `server.js` - Express server and API endpoints
- `db.js` - Database configuration
- `api.js` - Frontend API integration
- `index.html` - Main application page
- `init-db.sql` - Database schema and initial data

## Features

- Dynamic data loading from PostgreSQL
- Seamless scrolling team member list
- Real-time updates
- Responsive design
- Error handling

## API Endpoints

- `GET /api/team-members` - Get all team members
- `GET /api/work-orders` - Get top 3 work orders by votes
- `GET /api/navigators` - Get top 2 navigators by votes 