@echo off
echo 团队展示系统启动脚本
echo ==================

echo 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到Node.js，请先安装Node.js
    echo 下载地址：https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js环境正常

echo 检查依赖包...
if not exist "node_modules" (
    echo 正在安装依赖包...
    npm install
    if %errorlevel% neq 0 (
        echo 错误：依赖包安装失败
        pause
        exit /b 1
    )
) else (
    echo 依赖包已存在
)

echo 启动服务器...
echo 请在浏览器中访问：http://localhost:3000
echo 按Ctrl+C停止服务器
echo.

npm start
