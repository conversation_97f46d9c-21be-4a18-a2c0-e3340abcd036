const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'Qq147852!',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'postgres',
    schema: 'noc'  // 设置默认模式为 'noc'
});

// 测试数据库连接
pool.connect((err, client, release) => {
    if (err) {
        console.error('Error connecting to the database:', err.stack);
    } else {
        // 设置搜索路径为 'noc' 模式
        client.query('SET search_path TO noc', (err) => {
            if (err) {
                console.error('Error setting search path:', err.stack);
            } else {
                console.log('Successfully connected to database and set schema to noc');
            }
            release();
        });
    }
});

module.exports = {
    query: (text, params) => pool.query(text, params),
    pool
}; 