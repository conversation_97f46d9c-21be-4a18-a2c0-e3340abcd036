<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精英团队展示</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: url('images/背景.jpg') no-repeat center center;
            background-size: 100% 100%;
            background-attachment: fixed;
            min-height: 100vh;
        }
        .glass-card {
            display: flex;
            flex-direction: row;
            align-items: center;
            padding: 1.2rem;
            min-height: 150px;
            background: rgba(0, 0, 0, 0.2) !important;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            width: 48%; /* 适合两列布局的宽度 */
            margin-left: auto;
            margin-right: auto;
            color: white;
        }
        .glass-card img {
            width: 120px;
            height: 120px;
            /* margin-right: 1.5rem; */
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        .glass-card-content {
            flex: 1;
        }
        .hover-scale {
            transition: transform 0.3s ease;
        }
        .hover-scale:hover {
            transform: translateY(-5px);
        }
        /* 无缝滚动动画 */
        @keyframes cl-scrolling {
            0% {
                transform: translateY(0);
            }
            100% {
                transform: translateY(-50%);
            }
        }
        .team-carousel-vertical {
            display: flex;
            flex-direction: column;
            overflow: visible;
        }
        .team-container {
            width: calc(100% - 600px) !important; /* 减小左侧空间，从550px减到600px */
            margin-right: 1rem;
            margin-left: 20px; /* 减小左边距，从50px减到20px */
            display: flex;
            flex-direction: column;
        }
        .relative.overflow-hidden {
            overflow: hidden;
            position: relative;
            flex: 1; /* 占用所有可用空间 */
        }
        .team-member-list {
            display: block;
            width: 100%;
        }
        /* 确保卡片间距一致 */
        .glass-card.mb-4 {
            margin-bottom: 1rem !important;
        }
        /* 鼠标移入滚动区域变成手型 */
        .team-container:hover .team-carousel-vertical {
            cursor: pointer;
        }
        /* 鼠标移入滚动区域暂停动画 */
        .team-container.pause .team-carousel-vertical {
            animation-play-state: paused !important;
        }
        .team-page {
            display: none;
        }
        .team-page.active {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 1.5rem;
            height: 100%;
            padding-left: 0.5rem;
        }
        .pagination-dots {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 1.5rem;
        }
        .pagination-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #ccc;
            cursor: pointer;
        }
        .pagination-dot.active {
            background-color: #3b82f6;
        }
        @media (max-width: 1024px) {
            .team-page.active {
                grid-template-rows: unset;
                grid-template-columns: 1fr;
            }
        }
        @media (max-width: 640px) {
            .team-page.active {
                grid-template-columns: 1fr;
            }
        }
        .glass-card h3 {
            font-size: 1.3rem !important;
        }
        .glass-card p {
            font-size: 1rem !important;
        }
        .glass-card .text-sm {
            font-size: 0.85rem !important;
        }
        .glass-card h3,
        .glass-card .text-gray-800 {
            color: white !important;
        }
        .glass-card .text-gray-700 {
            color: rgba(255, 255, 255, 0.9) !important;
        }
        .glass-card .text-blue-600 {
            color: #93c5fd !important;
        }
        .glass-card .bg-blue-100 {
            background: rgba(147, 197, 253, 0.2) !important;
        }
        .glass-card .text-blue-800 {
            color: #bfdbfe !important;
        }
        .members-row {
            display: flex;
            justify-content: space-between;
            width: 100%;
            margin-bottom: 1.5rem;
            padding: 0 1rem; /* 增加左右内边距 */
        }
        .members-row .glass-card {
            margin: 0 1.5%;
            flex: 1;
            max-width: 47%; /* 稍微减小最大宽度，增加间距 */
        }
        @media (max-width: 768px) {
            .members-row {
                flex-direction: column;
            }
            .members-row .glass-card {
                margin-bottom: 1rem;
                width: 90%;
            }
        }
        /* 调整排行榜和导航员容器 */
        .lg\:w-1\/4.space-y-4 {
            margin-left: auto;
            width: 580px !important; /* 增加宽度，从500px增加到580px */
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 100%;
            gap: 0;
        }
        /* 调整整体布局 */
        .flex.flex-col.lg\:flex-row {
            align-items: stretch;
        }
        /* 特别为右侧排行榜和导航员容器中的卡片设置固定宽度 */
        .lg\:w-1\/4 .glass-card {
            width: 100%;
        }
        /* 整体布局调整 */
        .max-w-7xl {
            max-width: 100%;
            padding: 0 1rem;
        }
        /* 移动端适配 */
        @media (max-width: 1400px) {
            .team-container {
                width: calc(100% - 650px) !important;
                margin-left: 20px;
            }
        }
        @media (max-width: 1200px) {
            .flex.flex-col.lg\:flex-row {
                flex-direction: column !important;
            }
            .team-container, .lg\:w-1\/4 {
                width: 90% !important;
                margin: 0 auto 2rem auto;
            }
            .lg\:w-1\/4 .glass-card {
                width: 100% !important;
            }
        }
        /* 确保排行榜和领航员之间有适当间距 */
        .lg\:w-1\/4.space-y-4 .glass-card:first-child {
            margin-bottom: 1rem;
        }
        /* 适应变化的响应式样式 */
        @media (max-width: 1600px) {
            .team-container {
                width: calc(100% - 300px) !important;
            }
            .members-row {
                padding: 0 0.5rem;
            }
        }
        /* 调整工单排行和领航员照片对齐 */
        .rank-card {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        /* 添加彩色边框 */
        .rank-card.no1 .avatar {
            border: 3px solid #e53935 !important;
        }
        .rank-card.no2 .avatar {
            border: 3px solid #42a5f5 !important;
        }
        .rank-card.no3 .avatar {
            border: 3px solid #ff7043 !important;
        }
        .rank-card img.avatar,
        .top-ranking img.avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            margin-bottom: 15px;
            object-fit: cover;
            object-position: center 20%;
            border: 3px solid #42a5f5;
        }
        .rank-card .player-name {
            text-align: center;
            width: 100%;
        }
    </style>
</head>
<body class="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto">
        <!-- 标题区 -->
        <div class="mb-12 text-center relative">
            <img src="images/中国电信LOGO.png" alt="中国电信" class="absolute left-0 top-0 h-16 w-auto">
            <img src="images/NOC战新能力榜.png" alt="团队标题" class="mx-auto h-24 w-auto mb-6">
        </div>

        <div class="flex flex-col lg:flex-row gap-2 items-stretch">
            <!-- 左侧团队卡片区 -->
            <div class="lg:w-3/4 team-container order-1 lg:order-1" style="margin-left: -10px; margin-right: 0;">
                <div class="relative overflow-hidden">
                    <div class="team-carousel-vertical">
                        <div class="team-member-list">
                            <!-- 每行两个成员卡片 -->
                            <div class="members-row flex justify-between mb-4">
                                <!-- Member 1 -->
                                <div class="glass-card rounded-xl shadow-lg hover-scale" style="min-height: 120px;">
                                    <div class="flex items-center h-full">
                                        <img src="images/大网-黄潇聪.jpg" alt="成员照片" class="rounded-full object-cover border-4 border-white shadow">
                                        <div class="glass-card-content">
                                            <div class="flex items-center mb-2">
                                                <div>
                                                    <h3 class="text-xl font-bold text-gray-800">张伟</h3>
                                                    <p class="text-blue-600">技术研发团队</p>
                                                </div>
                                            </div>
                                            <p class="text-gray-700 mb-3">10年行业经验，专注于人工智能算法研发，带领团队完成多个国家级重点项目。</p>
                                            <div class="flex flex-wrap gap-2">
                                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">机器学习</span>
                                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">Python</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Member 2 -->
                                <div class="glass-card rounded-xl shadow-lg hover-scale" style="min-height: 120px;">
                                    <div class="flex items-center h-full">
                                        <img src="images/大网-王燕.png" alt="成员照片" class="rounded-full object-cover border-4 border-white shadow">
                                        <div class="glass-card-content">
                                            <div class="flex items-center mb-2">
                                                <div>
                                                    <h3 class="text-xl font-bold text-gray-800">李娜</h3>
                                                    <p class="text-blue-600">产品设计团队</p>
                                                </div>
                                            </div>
                                            <p class="text-gray-700 mb-3">8年用户体验设计经验，主导设计多款行业领先产品。</p>
                                            <div class="flex flex-wrap gap-2">
                                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">UI设计</span>
                                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">交互设计</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 第二行 -->
                            <div class="members-row flex justify-between mb-4">
                                <!-- Member 3 -->
                                <div class="glass-card rounded-xl shadow-lg hover-scale" style="min-height: 120px;">
                                    <div class="flex items-center h-full">
                                        <img src="images/大网-杨震.png" alt="成员照片" class="rounded-full object-cover border-4 border-white shadow">
                                        <div class="glass-card-content">
                                            <div class="flex items-center mb-2">
                                                <div>
                                                    <h3 class="text-xl font-bold text-gray-800">李娜2</h3>
                                                    <p class="text-blue-600">产品设计团队</p>
                                                </div>
                                            </div>
                                            <p class="text-gray-700 mb-3">8年用户体验设计经验，主导设计多款行业领先产品。</p>
                                            <div class="flex flex-wrap gap-2">
                                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">UI设计</span>
                                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">交互设计</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Member 4 -->
                                <div class="glass-card rounded-xl shadow-lg hover-scale" style="min-height: 120px;">
                                    <div class="flex items-center h-full">
                                        <img src="images/大网-何美方.png" alt="成员照片" class="rounded-full object-cover border-4 border-white shadow">
                                        <div class="glass-card-content">
                                            <div class="flex items-center mb-2">
                                                <div>
                                                    <h3 class="text-xl font-bold text-gray-800">李娜3</h3>
                                                    <p class="text-blue-600">产品设计团队</p>
                                                </div>
                                            </div>
                                            <p class="text-gray-700 mb-3">8年用户体验设计经验，主导设计多款行业领先产品。</p>
                                            <div class="flex flex-wrap gap-2">
                                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">UI设计</span>
                                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">交互设计</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                             <!-- 第三行 -->
                            <div class="members-row flex justify-between mb-4">
                                <!-- Member 5 -->
                                <div class="glass-card rounded-xl shadow-lg hover-scale" style="min-height: 120px;">
                                    <div class="flex items-center h-full">
                                        <img src="images/大网-程莎.jpg" alt="成员照片" class="rounded-full object-cover border-4 border-white shadow">
                                        <div class="glass-card-content">
                                            <div class="flex items-center mb-2">
                                                <div>
                                                    <h3 class="text-xl font-bold text-gray-800">李娜4</h3>
                                                    <p class="text-blue-600">产品设计团队</p>
                                                </div>
                                            </div>
                                            <p class="text-gray-700 mb-3">8年用户体验设计经验，主导设计多款行业领先产品。</p>
                                            <div class="flex flex-wrap gap-2">
                                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">UI设计</span>
                                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">交互设计</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Member 6 -->
                                <div class="glass-card rounded-xl shadow-lg hover-scale" style="min-height: 120px;">
                                    <div class="flex items-center h-full">
                                        <img src="images/大网-李远泽.png" alt="成员照片" class="rounded-full object-cover border-4 border-white shadow">
                                        <div class="glass-card-content">
                                            <div class="flex items-center mb-2">
                                                <div>
                                                    <h3 class="text-xl font-bold text-gray-800">李娜5</h3>
                                                    <p class="text-blue-600">产品设计团队</p>
                                                </div>
                                            </div>
                                            <p class="text-gray-700 mb-3">8年用户体验设计经验，主导设计多款行业领先产品。</p>
                                            <div class="flex flex-wrap gap-2">
                                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">UI设计</span>
                                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">交互设计</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- More members... -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧排行榜区 -->
            <div class="lg:w-1/4 space-y-4 flex flex-col justify-between order-2 lg:order-2">
                <div class="glass-card rounded-xl p-4 shadow-lg" style="height: 400px; width: 580px;">
                    <div class="text-center w-full">
                        <div class="ranking-title" style="display: flex; align-items: center; justify-content: center; margin-top: -5px; margin-bottom: 15px; font-size: 24px; font-weight: bold; color: white;">
                            <i class="fas fa-trophy text-yellow-400 mr-2"></i>本月云网导航员
                        </div>
                        <div class="top-ranking navigator-ranking" style="display: flex; justify-content: center; align-items: flex-end; margin-bottom: 20px;"></div>
                    </div>
                </div>
                <div class="glass-card rounded-xl p-4 shadow-lg mt-2" style="height: 400px; width: 580px;">
                    <div class="text-center w-full">
                        <div class="ranking-title" style="display: flex; align-items: center; justify-content: center; margin-top: -5px; margin-bottom: 15px; font-size: 24px; font-weight: bold; color: white;">
                            <i class="fas fa-trophy text-yellow-400 mr-2"></i>7月商机调用排行
                        </div>
                        <div class="top-ranking work-order-ranking" style="display: flex; justify-content: center; align-items: flex-end; margin-bottom: 10px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 页脚 -->
        <!--footer class="mt-16 text-center text-gray-600">
            <p>© 2023 公司名称. 保留所有权利.</p>
        </footer-->
    </div>

    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <script type="module">
        import { initializeData, initSeamlessScroll } from './api.js';

        document.addEventListener('DOMContentLoaded', function() {
            // Initialize data loading
            initializeData();
            
            // 窗口大小改变时重新计算
            window.addEventListener('resize', function() {
                setTimeout(() => {
                    initSeamlessScroll();
                }, 300);
            });
        });
    </script>
</body>
</html>
