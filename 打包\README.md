# 团队展示系统

这是一个基于Node.js和Express的团队展示系统，可以动态显示团队成员、工单排行榜和导航员信息。

## 项目结构

```
打包/
├── index.html          # 主页面
├── api.js             # 前端API调用和数据渲染
├── server.js          # Express服务器
├── data-loader.js     # Excel数据加载器
├── data.xlsx          # 数据源文件
├── package.json       # 项目依赖
├── images/            # 头像图片文件夹
└── README.md          # 说明文档
```

## 安装和运行

### 1. 安装依赖
```bash
npm install
```

### 2. 启动服务器
```bash
npm start
```

### 3. 访问应用
打开浏览器访问：http://localhost:3000

## 功能特性

- **团队成员展示**：无缝滚动显示团队成员信息
- **工单排行榜**：显示前3名工单处理员
- **导航员排行**：显示前2名导航员
- **实时数据更新**：支持Excel文件热更新
- **响应式设计**：适配不同屏幕尺寸

## 数据管理

### Excel文件结构
项目使用 `data.xlsx` 文件作为数据源，包含以下工作表：

1. **team_members** - 团队成员信息
   - name: 姓名
   - team: 团队
   - avatar: 头像文件名
   - introduction: 简介

2. **work_orders** - 工单信息
   - name: 姓名
   - orders: 工单数量
   - avatar: 头像文件名

3. **领航员** - 导航员信息
   - name: 姓名
   - avatar: 头像文件名
   - introduction: 简介

### 更新数据
1. 编辑 `data.xlsx` 文件
2. 将新的头像图片放入 `images/` 文件夹
3. 保存文件后系统会自动重新加载数据

## 技术栈

- **后端**: Node.js + Express
- **前端**: HTML5 + CSS3 + JavaScript (ES6+)
- **数据处理**: xlsx库处理Excel文件
- **实时通信**: Socket.IO
- **样式**: Tailwind CSS + 自定义CSS

## 注意事项

1. 确保头像图片文件名与Excel中的avatar字段完全匹配
2. 支持的图片格式：jpg, jpeg, png, JPG
3. 建议头像图片尺寸为正方形，系统会自动裁剪为圆形
4. 如果缺少头像文件，对应位置会显示空白

## 故障排除

### 图片不显示
- 检查images文件夹中是否存在对应的图片文件
- 确认文件名大小写是否匹配
- 检查图片文件是否损坏

### 数据不更新
- 确认Excel文件格式正确
- 检查工作表名称是否正确
- 重启服务器

## 开发者信息

如需修改样式或功能，主要文件说明：
- `index.html`: 页面结构和样式
- `api.js`: 前端逻辑和数据渲染
- `server.js`: 服务器配置和API端点
- `data-loader.js`: Excel数据解析逻辑
