// API endpoints
const API_ENDPOINTS = {
    TEAM_MEMBERS: '/api/team-members',
    WORK_ORDERS: '/api/work-orders',
    NAVIGATORS: '/api/navigators'
};

// Initialize Socket.IO connection
const socket = io();

// Listen for data updates
socket.on('dataUpdated', async () => {
    console.log('Data updated, refreshing...');
    await initializeData();
});

// Fetch team members data
async function fetchTeamMembers() {
    try {
        const response = await fetch(API_ENDPOINTS.TEAM_MEMBERS);
        if (!response.ok) throw new Error('Failed to fetch team members');
        return await response.json();
    } catch (error) {
        console.error('Error fetching team members:', error);
        return [];
    }
}

// Fetch work orders ranking data
async function fetchWorkOrders() {
    try {
        const response = await fetch(API_ENDPOINTS.WORK_ORDERS);
        if (!response.ok) throw new Error('Failed to fetch work orders');
        return await response.json();
    } catch (error) {
        console.error('Error fetching work orders:', error);
        return [];
    }
}

// Fetch navigators data
async function fetchNavigators() {
    try {
        const response = await fetch(API_ENDPOINTS.NAVIGATORS);
        if (!response.ok) throw new Error('Failed to fetch navigators');
        return await response.json();
    } catch (error) {
        console.error('Error fetching navigators:', error);
        return [];
    }
}

// Render team members
function renderTeamMembers(members) {
    const memberList = document.querySelector('.team-member-list');
    if (!memberList) return;

    const rows = [];
    for (let i = 0; i < members.length; i += 2) {
        const row = document.createElement('div');
        row.className = 'members-row flex justify-between mb-4';
        
        // First member in row
        if (members[i]) {
            row.appendChild(createMemberCard(members[i]));
        }
        
        // Second member in row
        if (members[i + 1]) {
            row.appendChild(createMemberCard(members[i + 1]));
        }
        
        rows.push(row);
    }
    
    memberList.innerHTML = '';
    rows.forEach(row => memberList.appendChild(row));
}

// Create member card
function createMemberCard(member) {
    const card = document.createElement('div');
    card.className = 'glass-card rounded-xl shadow-lg hover-scale';
    card.style.minHeight = '120px';
    card.style.position = 'relative';
    card.style.overflow = 'hidden';
    
    // 直接使用文件名（含扩展名）
    const avatarPath = member.avatar ? `images/${member.avatar}` : '';
    
    card.innerHTML = `
        <div class="flex items-center h-full">
            <img src="${avatarPath}" alt="${member.name}" class="rounded-full object-cover border-4 border-white shadow" style='margin-right: 1.5rem'>
            <div class="glass-card-content">
                <div class="flex items-center mb-2">
                    <div>
                        <h3 class="text-xl font-bold text-gray-800">${member.name}</h3>
                        <p class="text-blue-600">${member.team}</p>
                    </div>
                </div>
                <p class="text-gray-700 mb-3">${member.description}</p>
                <div class="flex flex-wrap gap-2">
                    ${member.skills.split(',').map(skill => `
                        <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">${skill}</span>
                    `).join('')}
                </div>
            </div>
        </div>
    `;
    
    return card;
}

// Render work orders ranking
function renderWorkOrders(orders) {
    const topRanking = document.querySelector('.work-order-ranking');
    if (!topRanking) return;
    
    // Reorder the array to put first place in the middle
    const reorderedOrders = [orders[1], orders[0], orders[2]];
    
    const rankingHTML = reorderedOrders.map((order, index) => {
        // Map the visual position to the actual rank
        const actualRank = index === 0 ? 2 : index === 1 ? 1 : 3;
        const marginBottom = index === 1 ? '40px' : '20px';
        const crownColor = actualRank === 1 ? 'gold' : actualRank === 2 ? '#42a5f5' : '#ff7043';
        const bannerColor = actualRank === 1 ? '#e53935' : actualRank === 2 ? '#42a5f5' : '#ff7043';
        
        // 直接使用文件名（含扩展名）
        const avatarPath = order.avatar ? `images/${order.avatar}` : '';
        
        return `
            <div class="rank-card no${actualRank}" style="display: flex; flex-direction: column; align-items: center; margin: 0 25px; margin-bottom: ${marginBottom}; width: 120px;">
                <div class="crown" style="position: relative; width: 60px; height: 35px; margin-bottom: 8px;">
                    <div style="font-size: 35px; color: ${crownColor}; position: absolute; left: 50%; transform: translateX(-50%);">👑</div>
                </div>
                <div class="rank-banner" style="background-color: ${bannerColor}; color: white; padding: 4px 18px; border-radius: 15px; margin-bottom: 12px; font-weight: bold; font-size: 17px;">NO.${actualRank}</div>
                <img src="${avatarPath}" alt="${order.name}" class="avatar">
                <div class="player-name" style="font-size: 15px; margin-bottom: 8px; color: #e0e0e0;">${order.name}</div>
                <div class="orders" style="font-size: 15px; color: #b0bec5;">${order.orders}单</div>
            </div>
        `;
    }).join('');
    
    topRanking.innerHTML = rankingHTML;
}

// Render navigators
function renderNavigators(navigators) {
    const navigatorContainer = document.querySelector('.navigator-ranking');
    if (!navigatorContainer) return;
    
    const navigatorHTML = navigators.map((navigator, index) => {
        // 直接使用文件名（含扩展名）
        const avatarPath = navigator.avatar ? `images/${navigator.avatar}` : '';
        
        return `
            <div class="rank-card no${index + 1}" style="display: flex; flex-direction: column; align-items: center; margin: 0 75px; margin-bottom: 100px; margin-top: -10px;width: 180px; position: relative;">
                <div style="display: flex; flex-direction: column; align-items: center;">
                    <img src="${avatarPath}" alt="${navigator.name}" class="avatar" style="width: 125px; height: 125px;">
                    <div class="player-name" style="font-size: 16px; margin-top: 8px; color: #e0e0e0;">${navigator.name}</div>
                </div>
                <div class="player-intro" style="position: absolute; top: 100%; left: 50%; transform: translateX(-50%); font-size: 15px; color: #b0bec5; text-align: center; width: 240px; line-height: 1.3; padding: 8px 0;">${navigator.introduction || ''}</div>
            </div>
        `;
    }).join('');
    
    navigatorContainer.innerHTML = navigatorHTML;
}

// Initialize data loading
async function initializeData() {
    try {
        const [teamMembers, workOrders, navigators] = await Promise.all([
            fetchTeamMembers(),
            fetchWorkOrders(),
            fetchNavigators()
        ]);
        
        renderTeamMembers(teamMembers);
        renderWorkOrders(workOrders);
        renderNavigators(navigators);
        
        // Initialize seamless scroll after data is loaded
        setTimeout(() => {
            initSeamlessScroll();
        }, 100);
    } catch (error) {
        console.error('Error initializing data:', error);
    }
}

// Initialize seamless scroll
function initSeamlessScroll() {
    const teamContainer = document.querySelector('.team-container');
    const rightSideContainer = document.querySelector('.lg\\:w-1\\/4.space-y-4');
    
    if (teamContainer && rightSideContainer) {
        const carousel = teamContainer.querySelector('.team-carousel-vertical');
        const memberList = carousel.querySelector('.team-member-list');
        const carouselWrapper = teamContainer.querySelector('.relative.overflow-hidden');
        
        // 获取右侧容器的总高度，用于确保左侧容器高度一致
        const rightContainerHeight = rightSideContainer.offsetHeight;
        
        // 设置左侧容器高度与右侧容器一致
        teamContainer.style.height = rightContainerHeight + 'px';
        
        // 设置滚动可视区域高度
        carouselWrapper.style.height = rightContainerHeight + 'px';
        
        // 移除之前的克隆内容（如果存在）
        const existingClones = carousel.querySelectorAll('.team-member-list:not(:first-child)');
        existingClones.forEach(clone => clone.remove());
        
        // 克隆列表内容
        const clonedContent = memberList.cloneNode(true);
        carousel.appendChild(clonedContent);
        
        // 计算内容高度
        const originalHeight = memberList.clientHeight;
        
        // 对于更宽的布局，增加滚动速度
        const duration = Math.max(40, Math.ceil(originalHeight / 15));
        
        // 应用动画
        carousel.style.animation = `cl-scrolling ${duration}s linear infinite`;
        
        // 确保容器样式
        teamContainer.style.overflow = 'hidden';
        teamContainer.style.position = 'relative';
        
        // 鼠标悬停暂停/恢复动画
        teamContainer.addEventListener('mouseenter', function() {
            carousel.style.animationPlayState = 'paused';
        });
        
        teamContainer.addEventListener('mouseleave', function() {
            carousel.style.animationPlayState = 'running';
        });
    }
}

// Export functions
export {
    initializeData,
    fetchTeamMembers,
    fetchWorkOrders,
    fetchNavigators,
    initSeamlessScroll
}; 