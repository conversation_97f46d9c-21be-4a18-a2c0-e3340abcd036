import openpyxl
import re
import os

# 获取 images 目录下所有图片文件名，建立名字到文件名的映射
image_dir = 'images'
image_files = os.listdir(image_dir)
name2file = {}
for file in image_files:
    name, ext = os.path.splitext(file)
    if ext.lower() in ['.jpg', '.jpeg', '.png']:
        name2file[name] = file

# 加载源数据文件
source_wb = openpyxl.load_workbook('市NOC战新特战队人员名单（大网数字团队-18人） (1).xlsx')
source_sheet = source_wb['特战队名单']

# 获取列标题并清理
def clean_title(title):
    """清理列标题中的HTML标签和括号内容"""
    return re.sub(r'<.*?>|\(.*?\)', '', title)

# 从第二行获取列标题（索引从1开始）
titles = {
    '云网': clean_title(source_sheet.cell(row=2, column=7).value),
    '产数': clean_title(source_sheet.cell(row=2, column=8).value),
    '研发': clean_title(source_sheet.cell(row=2, column=9).value)
}

# 准备新数据
new_data = []
for row in range(3, 51):  # 第3行到第20行（18条数据）
    # 提取各列数据
    name = source_sheet.cell(row=row, column=2).value  # B列
    team = source_sheet.cell(row=row, column=5).value  # E列
    description = source_sheet.cell(row=row, column=11).value  # K列
    
    # 处理技能组合
    skills = []
    
    # 添加工程师等级
    for col, prefix in [(7, titles['云网']), (8, titles['产数']), (9, titles['研发'])]:
        value = source_sheet.cell(row=row, column=col).value
        if value and value != '无' and value.strip():
            skills.append(f"{prefix}{value}")
    
    # 添加资质证书
    certs = source_sheet.cell(row=row, column=10).value  # J列
    if certs and certs != '无':
        for cert in certs.split('、'):
            if cert.strip():
                skills.append(cert.strip())
    
    # 组合技能字符串
    skills_str = ','.join(skills)
    
    # 匹配头像文件名
    avatar = name2file.get(str(name), "")  # 若找不到则留空
    
    # 添加到新数据集
    new_data.append([name, team, description, avatar, skills_str])

# 处理目标文件
target_wb = openpyxl.load_workbook('data.xlsx')
target_sheet = target_wb['team_members']

# 清除旧数据（保留标题行）
if target_sheet.max_row > 1:
    target_sheet.delete_rows(2, target_sheet.max_row - 1)

# 写入新数据
for row_data in new_data:
    target_sheet.append(row_data)

# 保存更改
target_wb.save('data.xlsx')
print("数据更新完成！更新记录数：", len(new_data))