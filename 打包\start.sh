#!/bin/bash

echo "团队展示系统启动脚本"
echo "=================="

echo "检查Node.js环境..."
if ! command -v node &> /dev/null; then
    echo "错误：未找到Node.js，请先安装Node.js"
    echo "下载地址：https://nodejs.org/"
    exit 1
fi

echo "Node.js环境正常"

echo "检查依赖包..."
if [ ! -d "node_modules" ]; then
    echo "正在安装依赖包..."
    npm install
    if [ $? -ne 0 ]; then
        echo "错误：依赖包安装失败"
        exit 1
    fi
else
    echo "依赖包已存在"
fi

echo "启动服务器..."
echo "请在浏览器中访问：http://localhost:3000"
echo "按Ctrl+C停止服务器"
echo

npm start
