const express = require('express');
const path = require('path');
const db = require('./db');
const app = express();
const port = 3000;

// Serve static files
app.use(express.static(path.join(__dirname)));

// API endpoints
app.get('/api/team-members', async (req, res) => {
    try {
        const result = await db.query('SELECT * FROM noc.team_members ORDER BY id');
        res.json(result.rows);
    } catch (error) {
        console.error('Error fetching team members:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

app.get('/api/work-orders', async (req, res) => {
    try {
        const result = await db.query('SELECT * FROM noc.work_orders ORDER BY orders DESC LIMIT 3');
        res.json(result.rows);
    } catch (error) {
        console.error('Error fetching work orders:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

app.get('/api/navigators', async (req, res) => {
    try {
        const result = await db.query('SELECT * FROM noc.navigators LIMIT 2');
        res.json(result.rows);
    } catch (error) {
        console.error('Error fetching navigators:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Start server
app.listen(port, () => {
    console.log(`Server running at http://localhost:${port}`);
}); 