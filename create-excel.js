const XLSX = require('xlsx');
const path = require('path');
const fs = require('fs');

try {
    console.log('开始创建Excel文件...');

    // 创建示例数据
    const teamMembers = [
        {
            name: '张伟',
            team: '技术研发团队',
            description: '10年行业经验，专注于人工智能算法研发，带领团队完成多个国家级重点项目。',
            avatar: '刘梅-工作照.png',
            skills: '机器学习,Python'
        },
        {
            name: '李娜',
            team: '产品设计团队',
            description: '8年用户体验设计经验，主导设计多款行业领先产品。',
            avatar: '黄潇聪-证件照.png',
            skills: 'UI设计,交互设计'
        },
        {
            name: '李娜2',
            team: '产品设计团队',
            description: '8年用户体验设计经验，主导设计多款行业领先产品。',
            avatar: '黄潇聪-证件照.png',
            skills: 'UI设计,交互设计'
        },
        {
            name: '李娜3',
            team: '产品设计团队',
            description: '8年用户体验设计经验，主导设计多款行业领先产品。',
            avatar: '黄潇聪-证件照.png',
            skills: 'UI设计,交互设计'
        },
        {
            name: '李娜4',
            team: '产品设计团队',
            description: '8年用户体验设计经验，主导设计多款行业领先产品。',
            avatar: '黄潇聪-证件照.png',
            skills: 'UI设计,交互设计'
        },
        {
            name: '李娜5',
            team: '产品设计团队',
            description: '8年用户体验设计经验，主导设计多款行业领先产品。',
            avatar: '黄潇聪-证件照.png',
            skills: 'UI设计,交互设计'
        }
    ];

    const workOrders = [
        {
            name: '张伟',
            avatar: '刘梅-工作照.png',
            orders: 4657546
        },
        {
            name: '李娜1',
            avatar: '黄潇聪-证件照.png',
            orders: 3425346
        },
        {
            name: '王强',
            avatar: '张昕01.png',
            orders: 2453654
        }
    ];

    const navigators = [
        {
            name: '张伟',
            avatar: '刘梅-工作照.png',
            introduction: '资深技术专家，10年行业经验'
        },
        {
            name: '李娜',
            avatar: '黄潇聪-证件照.png',
            introduction: '产品设计总监，8年用户体验设计经验'
        }
    ];

    console.log('数据准备完成，开始创建工作簿...');

    // 创建工作簿
    const workbook = XLSX.utils.book_new();

    // 创建工作表
    console.log('创建工作表...');
    const teamMembersSheet = XLSX.utils.json_to_sheet(teamMembers);
    const workOrdersSheet = XLSX.utils.json_to_sheet(workOrders);
    const navigatorsSheet = XLSX.utils.json_to_sheet(navigators);

    // 将工作表添加到工作簿
    console.log('添加工作表到工作簿...');
    XLSX.utils.book_append_sheet(workbook, teamMembersSheet, 'team_members');
    XLSX.utils.book_append_sheet(workbook, workOrdersSheet, 'work_orders');
    XLSX.utils.book_append_sheet(workbook, navigatorsSheet, '领航员');

    // 确保目标目录存在
    const filePath = path.join(__dirname, 'data.xlsx');
    console.log('准备写入文件到:', filePath);

    // 写入文件
    XLSX.writeFile(workbook, filePath);
    console.log('Excel文件创建成功！');

    // 验证文件是否创建成功
    if (fs.existsSync(filePath)) {
        console.log('文件已成功创建并保存');
    } else {
        throw new Error('文件创建失败');
    }

} catch (error) {
    console.error('创建Excel文件时发生错误:', error);
    process.exit(1);
} 